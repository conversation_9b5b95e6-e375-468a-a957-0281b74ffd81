const crypto = require('./extracted_crypto_function.js');

try {
    // 测试加密
    const encryptResult = crypto.encrypt('U31612173244185541', "15071127");
    console.log("加密结果:", encryptResult);
    // 测试解密
    const decryptResult = crypto.decrypt("U31612173244185541", 'c7185445c3f1220e0aca9be86a8731256edd8772b071994fbee56e209aae0775ab82f440ee0f0eda2e3cd3cdeb27a9e625b4b9a06df04e39');
    console.log("解密结果:", decryptResult);

} catch (error) {
    console.error("调用失败:", error.message);
}