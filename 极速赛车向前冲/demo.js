const crypto = require('./extracted_crypto_function.js');
const https = require('https');
const http = require('http');
const zlib = require('zlib');
const { URL } = require('url');

/**
 * 全局配置
 */
const baseUrl = "https://sdk.mini.stargame.group/api";
const token = "eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM";  // ⚠️注意替换

const headers = {
    "Authorization": `Bearer ${token}`,
    "Content-Type": "application/json",
    "Accept": "*/*",
    "xweb_xhr": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909"
};

/**
 * 通用请求方法
 */
function sendRequest(url, headers, payload = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const isHttps = urlObj.protocol === 'https:';
        const httpModule = isHttps ? https : http;

        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: payload ? 'POST' : 'GET',
            headers: headers,
            // 代理设置
            agent: new (isHttps ? https.Agent : http.Agent)({
                proxy: 'http://127.0.0.1:2024'
            }),
            rejectUnauthorized: false
        };

        const req = httpModule.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve(data);
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (payload) {
            req.write(JSON.stringify(payload));
        }

        req.end();
    });
}

/**
 * 获取存档
 */
async function getArchive(baseUrl, headers) {
    try {
        const url = baseUrl + "/game_user/get_game_archive"; // ⚠️确认路径
        const response = await sendRequest(url, headers);
        const res = JSON.parse(response);

        if (!res || !res.succeed) {
            throw new Error("获取存档失败: " + response);
        }

        const archiveHex = res.data.archive.replace(/\n/g, "");
        const binaryData = Buffer.from(archiveHex, 'hex');

        const decoded = zlib.gunzipSync(binaryData).toString();
        console.log("解压后的原始内容:", decoded);

        const data = JSON.parse(decoded);
        console.log("当前存档内容:");
        console.log(JSON.stringify(data, null, 2));

        return {
            data: data,
            version: parseInt(res.data.version)
        };
    } catch (error) {
        console.error("获取存档失败:", error.message);
        throw error;
    }
}

/**
 * 修改并上传存档
 */
async function updateArchive(baseUrl, headers, data, version) {
    try {
        const url = baseUrl + "/game_user/sync_game_archive";

        // 示例：改金币为 999999
        data['5'] = 999999;

        // 压缩 + 转 HEX
        const jsonStr = JSON.stringify(data);
        const gzData = zlib.gzipSync(jsonStr);
        const archiveHex = gzData.toString('hex');

        const payload = {
            "version": version, // 一般需要递增
            "archive": archiveHex
        };

        const response = await sendRequest(url, headers, payload);
        console.log("上传结果:");
        console.log(response);
    } catch (error) {
        console.error("上传存档失败:", error.message);
        throw error;
    }
}

/**
 * 主逻辑
 */
async function main() {
    try {
        console.log("开始获取存档...");
        const archiveInfo = await getArchive(baseUrl, headers);

        console.log("\n存档获取成功，版本:", archiveInfo.version);

        // 调试时可以先注释上传
        console.log("程序结束 (上传功能已注释)");
        return;

        // 取消注释下面这行来启用上传功能
        // await updateArchive(baseUrl, headers, archiveInfo.data, archiveInfo.version);

    } catch (error) {
        console.error("主程序执行失败:", error.message);
    }
}

/**
 * 测试新的 signData 方法
 */
function testSignDataMethod() {
    try {


        // 准备测试数据 - signData 需要一个对象作为第一个参数
        const dataObject = {
            "5": 999999,  // 金币
            "someKey": "someValue",
            "anotherKey": 123,
            "numberValue": 456
        };

        const key = "U31612173244185541";  // 密钥

        const result = crypto.signData(dataObject, key);
        console.log("signData 结果:", result);

        // 显示处理后的数据对象
        console.log("\n处理后的数据对象:", JSON.stringify(dataObject, null, 2));

        return result;

    } catch (error) {
        console.error("signData 调用失败:", error.message);
        console.error("错误详情:", error);
        return null;
    }
}

// 执行测试
console.log("开始测试 signData 方法...");
testSignDataMethod();

// 如果需要测试存档功能，取消注释下面这行
// main();