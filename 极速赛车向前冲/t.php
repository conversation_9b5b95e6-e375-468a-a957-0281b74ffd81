<?php

$archive_hex = '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';


$binary_data = hex2bin($archive_hex);
if ($binary_data === false) {
    die("hex2bin 转换失败");
}

$decoded = gzdecode($binary_data);
if ($decoded === false) {
    die("gzdecode 解压失败");
}
var_dump($decoded);